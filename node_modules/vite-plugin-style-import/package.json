{"name": "vite-plugin-style-import", "version": "2.0.0", "description": "A plug-in that imports component library styles on demand", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "license": "MIT", "author": "Vben", "files": ["dist"], "keywords": ["vite", "style", "import", "css"], "repository": {"type": "git", "url": "https://github.com/anncwb/vite-plugin-style-import", "directory": "packages/core"}, "bugs": {"url": "https://github.com/anncwb/vite-plugin-style-import/issues"}, "homepage": "https://github.com/anncwb/vite-plugin-style-import/tree/master/#readme", "dependencies": {"@rollup/pluginutils": "^4.1.2", "change-case": "^4.1.2", "console": "^0.7.2", "es-module-lexer": "^0.9.3", "fs-extra": "^10.0.0", "magic-string": "^0.25.7", "pathe": "^0.2.0"}, "peerDependencies": {"vite": ">=2.0.0"}, "devDependencies": {"@types/fs-extra": "^9.0.13", "@types/node": "^17.0.13"}, "scripts": {"dev": "pnpm unbuild --stub", "build": "pnpm unbuild"}}