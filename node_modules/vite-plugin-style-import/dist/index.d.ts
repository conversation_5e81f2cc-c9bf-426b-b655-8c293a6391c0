import { Plugin } from 'vite';

declare type RegOptions = string | RegExp | (string | RegExp)[] | null | undefined;
interface Lib {
    importTest?: RegExp;
    /**
     * Dependent library name
     */
    libraryName: string;
    /**
     * When the imported style file does not end with .css, it needs to be turned on
     * @default: false
     */
    esModule?: boolean;
    /**
     * Custom imported component style conversion
     */
    resolveStyle?: (name: string) => string;
    /**
     * There may be some component libraries that are not very standardized.
     * You can turn on this to ignore to determine whether the file exists. Prevent errors when importing non-existent css files.
     * Performance may be slightly reduced after it is turned on, but the impact is not significant
     * @default: false
     */
    ensureStyleFile?: boolean;
    /**
     * Customize imported component file name style conversion
     * @default: paramCase
     */
    libraryNameChangeCase?: LibraryNameChangeCase;
    /**
     * Whether to introduce base style
     */
    base?: string;
}
interface VitePluginOptions {
    include?: RegOptions;
    exclude?: RegOptions;
    /**
     * @default process.cwd()
     * @deprecated 1.2.0 is obsolete
     */
    root?: string;
    libs?: Lib[];
    resolves?: Lib[];
}
interface Source {
    opts: {
        libs: Lib[];
        cacheDir: string;
    };
}
declare type LibraryNameChangeCase = ChangeCaseType | ((name: string) => string);
declare type ChangeCaseType = 'camelCase' | 'capitalCase' | 'constantCase' | 'dotCase' | 'headerCase' | 'noCase' | 'paramCase' | 'pascalCase' | 'pathCase' | 'sentenceCase' | 'snakeCase';

declare function AntdResolve(): Lib;

declare function AndDesignVueResolve(): Lib;

declare function ElementPlusResolve(): Lib;

declare function VantResolve(): Lib;

declare function NutuiResolve(): Lib;

declare function VxeTableResolve(): Lib;

declare function createStyleImportPlugin(options: VitePluginOptions): Plugin;
declare function transformImportVar(importStr: string): readonly string[];
declare function getChangeCaseFileName(importedName: string, libraryNameChangeCase: LibraryNameChangeCase): string;

export { AndDesignVueResolve, AntdResolve, ChangeCaseType, ElementPlusResolve, Lib, LibraryNameChangeCase, NutuiResolve, RegOptions, Source, VantResolve, VitePluginOptions, VxeTableResolve, createStyleImportPlugin, getChangeCaseFileName, transformImportVar };
