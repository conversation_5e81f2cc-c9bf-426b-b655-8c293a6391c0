{"name": "es-module-lexer", "version": "0.9.3", "description": "Lexes ES modules returning their import/export metadata", "main": "dist/lexer.cjs", "module": "dist/lexer.js", "types": "types/lexer.d.ts", "exports": {".": {"module": "./dist/lexer.js", "import": "./dist/lexer.js", "require": "./dist/lexer.cjs"}, "./js": "./dist/lexer.asm.js"}, "scripts": {"test:js": "mocha -b -u tdd test/*.cjs", "test:wasm": "cross-env WASM=1 mocha -b -u tdd test/*.cjs", "test": "npm run test:js && npm run test:wasm", "build": "npm run build:wasm && npm run build:asm && npm run build:cjs", "build:cjs": "babel dist/lexer.js | terser -c -m -o dist/lexer.cjs", "build:wasm": "node build.js", "build:asm": "cat src/lexer.asm.js lib/lexer.asm.js | terser --module -c -m -o dist/lexer.asm.js", "bench": "node --expose-gc bench/index.js", "prepublishOnly": "npm run build", "footprint": "npm run build && echo Wasm: && cat dist/lexer.js | brotli | wc -c && echo Asm.js: && cat dist/lexer.asm.js | brotli | wc -c"}, "author": "<PERSON>", "license": "MIT", "devDependencies": {"@babel/cli": "^7.5.5", "@babel/core": "^7.5.5", "@babel/plugin-transform-modules-commonjs": "^7.5.0", "cross-env": "^7.0.3", "kleur": "^2.0.2", "mocha": "^5.2.0", "terser": "^4.1.4"}, "files": ["dist", "types", "lexer.js"], "type": "module", "repository": {"type": "git", "url": "git+https://github.com/guybedford/es-module-lexer.git"}, "bugs": {"url": "https://github.com/guybedford/es-module-lexer/issues"}, "homepage": "https://github.com/guybedford/es-module-lexer#readme"}