# EmulatorJS Core: pcsx_rearmed

This package contains the stable EmulatorJS core: pcsx_rearmed

Lean more about EmulatorJS at https://emulatorjs.org

Core repository:
https://github.com/EmulatorJS/pcsx_rearmed

Core is build using this repository:
https://github.com/EmulatorJS/build

## How to install

To install core, run the following command:

```bash
npm install @emulatorjs/core-pcsx_rearmed
```
To install all cores, run the following command:

```bash
npm install @emulatorjs/cores
```

