# EmulatorJS Core: yabause

This package contains the stable EmulatorJS core: ya<PERSON><PERSON>

Lean more about EmulatorJS at https://emulatorjs.org

Core repository:
https://github.com/EmulatorJS/yabause

Core is build using this repository:
https://github.com/EmulatorJS/build

## How to install

To install core, run the following command:

```bash
npm install @emulatorjs/core-yabause
```
To install all cores, run the following command:

```bash
npm install @emulatorjs/cores
```

