# EmulatorJS Core: genesis_plus_gx

This package contains the stable EmulatorJS core: genesis_plus_gx

Lean more about EmulatorJS at https://emulatorjs.org

Core repository:
https://github.com/EmulatorJS/Genesis-Plus-GX

Core is build using this repository:
https://github.com/EmulatorJS/build

## How to install

To install core, run the following command:

```bash
npm install @emulatorjs/core-genesis_plus_gx
```
To install all cores, run the following command:

```bash
npm install @emulatorjs/cores
```

