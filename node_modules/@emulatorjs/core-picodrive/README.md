# EmulatorJS Core: picodrive

This package contains the stable EmulatorJS core: picodrive

Lean more about EmulatorJS at https://emulatorjs.org

Core repository:
https://github.com/EmulatorJS/picodrive

Core is build using this repository:
https://github.com/EmulatorJS/build

## How to install

To install core, run the following command:

```bash
npm install @emulatorjs/core-picodrive
```
To install all cores, run the following command:

```bash
npm install @emulatorjs/cores
```

