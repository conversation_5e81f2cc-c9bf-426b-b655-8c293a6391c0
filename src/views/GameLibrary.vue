<template>
  <div class="min-h-screen p-4 md:p-8">
    <!-- 页面标题 -->
    <div class="max-w-6xl mx-auto mb-8">
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-3xl md:text-4xl font-bold text-gradient">🎮 游戏库</h1>
        <router-link to="/" class="btn-secondary flex items-center gap-2">
          <span>←</span>
          返回首页
        </router-link>
      </div>

      <p class="text-gray-300 text-lg">选择游戏或上传你的游戏文件开始游戏</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="max-w-6xl mx-auto space-y-8">
      <!-- 内置游戏选择 -->
      <section class="card p-6">
        <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
          <span>🎯</span>
          内置游戏
        </h2>
        <GameSelector @gameSelected="handleGameSelected" @error="handleError" />
      </section>

      <!-- 分隔线 -->
      <div class="relative text-center">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-600"></div>
        </div>
        <div class="relative flex justify-center text-sm">
          <span
            class="px-4 py-2 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-full font-medium"
          >
            或者
          </span>
        </div>
      </div>

      <!-- 文件上传 -->
      <section class="card p-6">
        <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
          <span>📁</span>
          上传游戏文件
        </h2>
        <FileUploader @files-selected="handleFilesSelected" />
      </section>
    </div>

    <!-- 错误提示 -->
    <div v-if="error" class="fixed bottom-4 right-4 max-w-md z-50">
      <div
        class="bg-red-500/20 border border-red-500/30 backdrop-blur-md rounded-xl p-4 text-white shadow-2xl"
      >
        <div class="flex items-start gap-3">
          <span class="text-red-400 text-xl">❌</span>
          <div class="flex-1">
            <p class="text-sm">{{ error }}</p>
            <button
              @click="error = null"
              class="mt-2 text-xs bg-red-500/30 hover:bg-red-500/50 px-3 py-1 rounded-lg transition-colors"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";
import type { GameFile } from "../types/emulator";
import GameSelector from "../components/GameSelector.vue";
import FileUploader from "../components/FileUploader.vue";
import { gameStorage } from "../utils/gameStorage";

const router = useRouter();
const error = ref<string | null>(null);

async function handleGameSelected(game: GameFile) {
  console.log("选择的游戏:", game);
  console.log("游戏数据大小:", game.data?.byteLength || 0, "字节");

  try {
    // 使用 IndexedDB 存储游戏数据
    const gameId = await gameStorage.saveGame(game);
    console.log("游戏数据已保存到 IndexedDB，ID:", gameId);

    // 只在 sessionStorage 中存储游戏 ID
    sessionStorage.setItem("selectedGameId", gameId);

    router.push("/play");
  } catch (error) {
    console.error("保存游戏数据失败:", error);
    alert("保存游戏数据失败，请重试");
  }
}

function handleFilesSelected(games: GameFile[]) {
  if (games.length > 0) {
    handleGameSelected(games[0]);
  }
}

function handleError(errorMessage: string) {
  error.value = errorMessage;
}
</script>
