<template>
  <div class="min-h-screen p-4 md:p-8">
    <div class="max-w-4xl mx-auto">
      <!-- 页面标题 -->
      <div class="text-center mb-12">
        <router-link
          to="/"
          class="btn-secondary inline-flex items-center gap-2 mb-6"
        >
          <span>←</span>
          返回首页
        </router-link>

        <h1 class="text-4xl md:text-5xl font-bold text-gradient mb-4">
          关于游戏模拟器
        </h1>
        <p class="text-xl text-gray-300">在浏览器中重温经典游戏的魅力</p>
      </div>

      <!-- 内容区域 -->
      <div class="space-y-8">
        <!-- 项目介绍 -->
        <section class="card p-8">
          <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
            <span>🎮</span>
            项目介绍
          </h2>
          <div class="text-gray-300 space-y-4">
            <p>
              这是一个基于 Web
              技术的游戏模拟器，让你可以在浏览器中直接运行经典的掌机和家用机游戏。
              无需下载安装任何软件，只需上传游戏文件即可开始游戏。
            </p>
            <p>
              项目使用了 EmulatorJS 作为核心模拟器引擎，支持多种经典游戏平台，
              并针对移动设备进行了优化，提供了良好的触屏游戏体验。
            </p>
          </div>
        </section>

        <!-- 支持的平台 -->
        <section class="card p-8">
          <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
            <span>🎯</span>
            支持的游戏平台
          </h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-black/20 rounded-lg p-4">
              <h3 class="font-semibold mb-2">Game Boy / Game Boy Color</h3>
              <p class="text-sm text-gray-400">支持 .gb, .gbc 格式</p>
            </div>
            <div class="bg-black/20 rounded-lg p-4">
              <h3 class="font-semibold mb-2">Game Boy Advance</h3>
              <p class="text-sm text-gray-400">支持 .gba 格式</p>
            </div>
            <div class="bg-black/20 rounded-lg p-4">
              <h3 class="font-semibold mb-2">Nintendo Entertainment System</h3>
              <p class="text-sm text-gray-400">支持 .nes 格式</p>
            </div>
            <div class="bg-black/20 rounded-lg p-4">
              <h3 class="font-semibold mb-2">Super Nintendo</h3>
              <p class="text-sm text-gray-400">支持 .snes, .smc 格式</p>
            </div>
          </div>
        </section>

        <!-- 技术栈 -->
        <section class="card p-8">
          <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
            <span>⚡</span>
            技术栈
          </h2>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-4 bg-black/20 rounded-lg">
              <div class="text-2xl mb-2">🟢</div>
              <div class="font-semibold">Vue 3</div>
              <div class="text-xs text-gray-400">前端框架</div>
            </div>
            <div class="text-center p-4 bg-black/20 rounded-lg">
              <div class="text-2xl mb-2">⚡</div>
              <div class="font-semibold">Vite</div>
              <div class="text-xs text-gray-400">构建工具</div>
            </div>
            <div class="text-center p-4 bg-black/20 rounded-lg">
              <div class="text-2xl mb-2">🎨</div>
              <div class="font-semibold">Tailwind CSS</div>
              <div class="text-xs text-gray-400">样式框架</div>
            </div>
            <div class="text-center p-4 bg-black/20 rounded-lg">
              <div class="text-2xl mb-2">🎮</div>
              <div class="font-semibold">EmulatorJS</div>
              <div class="text-xs text-gray-400">模拟器引擎</div>
            </div>
          </div>
        </section>

        <!-- 使用说明 -->
        <section class="card p-8">
          <h2 class="text-2xl font-bold mb-4 flex items-center gap-2">
            <span>📖</span>
            使用说明
          </h2>
          <div class="space-y-4">
            <div class="flex items-start gap-3">
              <span
                class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold"
                >1</span
              >
              <div>
                <h3 class="font-semibold">选择游戏</h3>
                <p class="text-gray-400 text-sm">
                  从内置游戏中选择或上传你的游戏文件
                </p>
              </div>
            </div>
            <div class="flex items-start gap-3">
              <span
                class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold"
                >2</span
              >
              <div>
                <h3 class="font-semibold">选择模拟器核心</h3>
                <p class="text-gray-400 text-sm">
                  系统会自动推荐最适合的模拟器核心
                </p>
              </div>
            </div>
            <div class="flex items-start gap-3">
              <span
                class="bg-primary-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold"
                >3</span
              >
              <div>
                <h3 class="font-semibold">开始游戏</h3>
                <p class="text-gray-400 text-sm">
                  点击启动游戏按钮，享受游戏乐趣
                </p>
              </div>
            </div>
          </div>
        </section>

        <section class="card p-8 text-center">
          <h2 class="text-2xl font-bold mb-4">版权声明</h2>
          <p class="text-gray-400 text-sm">
            本项目仅供学习和研究使用。请确保你拥有所上传游戏的合法版权。
            我们不提供任何受版权保护的游戏文件。
          </p>
        </section>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// About page component logic
</script>
