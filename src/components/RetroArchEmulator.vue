<template>
  <div :class="['w-full', isFullscreen ? 'mobile-fullscreen' : 'space-y-6']">
    <!-- Game Container -->
    <div
      :class="[
        'relative bg-black rounded-xl overflow-hidden shadow-2xl',
        isFullscreen ? 'w-full h-full' : 'game-viewport mx-auto',
      ]"
    >
      <!-- Loading Overlay -->
      <div
        v-if="loading"
        class="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-10"
      >
        <div class="text-center space-y-4">
          <div class="relative">
            <div class="loading-spinner mx-auto"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
            <div class="pulse-ring"></div>
          </div>
          <h3 class="text-xl font-semibold text-white">
            正在加载RetroArch模拟器...
          </h3>
          <p class="text-gray-300">正在下载 {{ core?.name }} 核心...</p>
          <div class="w-64 bg-gray-700 rounded-full h-2 mx-auto">
            <div
              class="bg-primary-500 h-2 rounded-full animate-pulse"
              style="width: 45%"
            ></div>
          </div>
        </div>
      </div>

      <!-- Game Canvas -->
      <div
        id="canvas"
        class="w-full h-full flex items-center justify-center bg-black"
      ></div>
    </div>

    <!-- Controls -->
    <div
      :class="[
        'game-controls',
        isFullscreen ? 'fixed bottom-0 left-0 right-0 z-30 rounded-none' : '',
      ]"
    >
      <button
        v-if="!loading"
        @click="startGame"
        class="btn-primary flex items-center gap-2"
      >
        <span class="text-lg">🎮</span>
        启动游戏
      </button>

      <button
        @click="stopEmulator"
        class="btn-secondary flex items-center gap-2"
      >
        <span class="text-lg">⏹️</span>
        停止游戏
      </button>

      <button
        @click="$emit('back')"
        class="btn-secondary flex items-center gap-2"
      >
        <span class="text-lg">←</span>
        返回选择
      </button>

      <!-- Status Indicator -->
      <div class="flex items-center gap-2">
        <div
          :class="[
            'status-indicator',
            loading ? 'status-loading' : 'status-playing',
          ]"
        >
          <div class="w-2 h-2 rounded-full bg-current animate-pulse mr-2"></div>
          {{ loading ? "加载中" : "运行中" }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed, watch } from "vue";
import type { GameFile, EmulatorCore } from "../types/emulator";

const props = defineProps<{
  game: GameFile;
  core: EmulatorCore | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  error: [message: string];
  stop: [];
  back: [];
}>();

const loading = ref(true);
const isFullscreen = ref(false);
const isPortrait = ref(window.innerHeight > window.innerWidth);

// 检测是否为移动设备
const isMobile = computed(() => {
  return (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    ) || window.innerWidth <= 768
  );
});

// 监听屏幕方向变化
function handleOrientationChange() {
  isPortrait.value = window.innerHeight > window.innerWidth;
  console.log("屏幕方向变化:", isPortrait.value ? "竖屏" : "横屏");
}

// 切换全屏模式
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;

  if (isFullscreen.value) {
    // 进入全屏
    document.body.style.overflow = "hidden";
    // 尝试进入浏览器全屏模式
    if (document.documentElement.requestFullscreen) {
      document.documentElement.requestFullscreen().catch(() => {
        // 如果全屏失败，只使用CSS全屏
      });
    }
  } else {
    // 退出全屏
    document.body.style.overflow = "";
    if (document.fullscreenElement && document.exitFullscreen) {
      document.exitFullscreen().catch(() => {
        // 如果退出全屏失败，只使用CSS退出
      });
    }
  }
}

onMounted(() => {
  // 添加屏幕方向变化监听器
  window.addEventListener("resize", handleOrientationChange);
  window.addEventListener("orientationchange", handleOrientationChange);
});

// 监听 props 变化，当游戏和核心都准备好时初始化
watch(
  () => [props.game, props.core],
  async ([game, core]) => {
    if (game && core) {
      console.log("游戏和核心都准备好了，开始初始化...", {
        game: game.name,
        core: core.name,
      });
      try {
        await initializeRetroArch();
      } catch (error) {
        console.error("RetroArch初始化失败:", error);
        emit("error", "RetroArch初始化失败: " + (error as Error).message);
      }
    }
  },
  { immediate: true }
);

async function initializeRetroArch() {
  try {
    loading.value = true;

    console.log("开始加载EmulatorJS模拟器...");

    // 设置EmulatorJS配置
    const gameUrl = await createGameBlob();
    const coreType = getEmulatorJSCore(props.core?.id || "gambatte");
    const systemType = getSystemFromCore(props.core?.id || "gambatte");

    console.log("游戏URL:", gameUrl);
    console.log("核心类型:", coreType);
    console.log("系统类型:", systemType);

    // 设置回调函数（必须在加载器之前设置）
    (window as any).EJS_ready = () => {
      console.log("🎮 EmulatorJS ready!");
      loading.value = false;
    };

    (window as any).EJS_onGameStart = () => {
      console.log("🎮 Game started!");
      loading.value = false;
    };

    (window as any).EJS_onLoadState = () => {
      console.log("🎮 Game loaded!");
    };

    // 配置EmulatorJS全局变量
    (window as any).EJS_player = "#canvas";
    (window as any).EJS_core = coreType;
    (window as any).EJS_system = systemType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_gameName = props.game.name;
    (window as any).EJS_startOnLoaded = true;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;

    console.log("EmulatorJS配置:", {
      player: (window as any).EJS_player,
      core: (window as any).EJS_core,
      system: (window as any).EJS_system,
      pathtodata: (window as any).EJS_pathtodata,
      gameUrl: (window as any).EJS_gameUrl,
      gameName: (window as any).EJS_gameName,
      startOnLoaded: (window as any).EJS_startOnLoaded,
    });

    // 验证游戏 URL 是否可访问
    console.log("验证游戏 URL:", gameUrl);
    fetch(gameUrl)
      .then((response) => {
        console.log(
          "游戏文件响应:",
          response.status,
          response.headers.get("content-type")
        );
        return response.arrayBuffer();
      })
      .then((buffer) => {
        console.log("游戏文件大小:", buffer.byteLength, "字节");
      })
      .catch((error) => {
        console.error("游戏文件访问失败:", error);
      });

    // 动态加载EmulatorJS加载器
    await loadEmulatorJS();
    // 给EmulatorJS一些时间来初始化
    setTimeout(() => {
      if (loading.value) {
        console.log("EmulatorJS初始化超时，尝试手动启动...");
        loading.value = false;
      }
    }, 15000); // 增加到15秒超时
  } catch (error) {
    loading.value = false;
    throw error;
  }
}

function getEmulatorJSCore(coreId: string): string {
  // EmulatorJS核心名称映射 - 直接返回核心名称
  const coreMap: { [key: string]: string } = {
    gambatte: "gambatte",
    mgba: "mgba",
    snes9x: "snes9x",
    fceumm: "fceumm",
    genesis_plus_gx: "genesis_plus_gx",
    melonds: "melonds",
    desmume: "desmume",
  };

  return coreMap[coreId] || "gambatte";
}

function getSystemFromCore(coreId: string): string {
  const systemMap: { [key: string]: string } = {
    gambatte: "gb",
    mgba: "gba",
    snes9x: "snes",
    fceumm: "nes",
    genesis_plus_gx: "segaMD",
    melonds: "nds",
    desmume: "nds",
  };

  return systemMap[coreId] || "gb";
}

async function createGameBlob(): Promise<string> {
  try {
    console.log("原始游戏数据:", props.game.data);
    console.log("游戏数据类型:", typeof props.game.data);
    console.log("游戏数据是否为数组:", Array.isArray(props.game.data));

    // 将游戏数据转换为Blob URL
    const gameData = new Uint8Array(props.game.data);
    console.log("游戏数据大小:", gameData.length, "字节");

    // 根据文件扩展名设置正确的MIME类型
    const extension = props.game.name.split(".").pop()?.toLowerCase();
    let mimeType = "application/octet-stream";

    if (extension === "zip") {
      mimeType = "application/zip";
    } else if (extension === "gb" || extension === "gbc") {
      mimeType = "application/x-gameboy-rom";
    } else if (extension === "gba") {
      mimeType = "application/x-gba-rom";
    }

    const blob = new Blob([gameData], { type: mimeType });
    const url = URL.createObjectURL(blob);

    console.log("创建Blob URL:", url);
    console.log("MIME类型:", mimeType);

    return url;
  } catch (error) {
    console.error("创建游戏Blob失败:", error);
    throw new Error("无法创建游戏文件");
  }
}

async function loadEmulatorJS() {
  return new Promise<void>((resolve, reject) => {
    // 清理之前的实例
    if ((window as any).EJS_emulator) {
      try {
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("清理旧实例失败:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 保存当前配置
    const gameUrl = (window as any).EJS_gameUrl;
    const coreType = (window as any).EJS_core;
    const systemType = (window as any).EJS_system;

    // 重新设置配置
    (window as any).EJS_player = "#canvas";
    (window as any).EJS_core = coreType;
    (window as any).EJS_system = systemType;
    (window as any).EJS_pathtodata = "/emulatorjs/";
    (window as any).EJS_gameUrl = gameUrl;
    (window as any).EJS_startOnLoaded = false;
    (window as any).EJS_threads = false;
    (window as any).EJS_volume = 0.8;

    // 检查是否已经加载了脚本
    const existingScript = document.querySelector(
      'script[src="/emulatorjs/loader.js"]'
    );
    if (existingScript) {
      existingScript.remove();
    }

    const script = document.createElement("script");
    script.src = "/emulatorjs/loader.js";
    script.async = true;

    script.onload = () => {
      console.log("✅ EmulatorJS加载器脚本加载成功");
      // 给脚本一些时间来初始化
      setTimeout(() => {
        if ((window as any).EJS_emulator) {
          console.log("✅ EmulatorJS初始化成功");

          // 添加事件监听器
          console.log("🎮 模拟器准备就绪");
          // 尝试自动启动游戏
          setTimeout(() => {
            try {
              console.log("尝试自动启动游戏...");
              // 查找并点击开始按钮
              const startButton = document.querySelector(".ejs_start_button");
              if (startButton) {
                console.log("找到开始按钮，自动点击...");
                (startButton as HTMLElement).click();
              } else {
                console.log("未找到开始按钮");
              }
            } catch (error) {
              console.log("自动启动游戏失败:", error);
            }
          }, 100);

          (window as any).EJS_emulator.on("start", () => {
            console.log("🎮 游戏开始");
            loading.value = false;
          });

          (window as any).EJS_emulator.on("error", (error: any) => {
            console.error("❌ EmulatorJS错误:", error);
            loading.value = false;
            emit("error", `模拟器错误: ${error}`);
          });

          resolve();
        } else {
          console.log("等待EmulatorJS初始化...");
          resolve(); // 即使没有立即初始化也继续
        }
      }, 300); // 增加到3秒等待时间
    };

    script.onerror = (error) => {
      console.error("❌ EmulatorJS加载器脚本加载失败:", error);
      reject(new Error("无法加载EmulatorJS加载器"));
    };

    document.head.appendChild(script);
  });
}

function startGame() {
  try {
    if ((window as any).EJS_emulator) {
      console.log("手动启动游戏...");
      // 尝试不同的启动方法
      if ((window as any).EJS_emulator.startGame) {
        (window as any).EJS_emulator.startGame();
      } else if (
        (window as any).EJS_emulator.gameManager &&
        (window as any).EJS_emulator.gameManager.start
      ) {
        (window as any).EJS_emulator.gameManager.start();
      } else {
        // 模拟点击开始按钮
        const startButton = document.querySelector(".ejs_start_button");
        if (startButton) {
          console.log("点击开始按钮...");
          (startButton as HTMLElement).click();
        } else {
          console.log("未找到开始按钮");
        }
      }
    } else {
      console.log("模拟器未准备就绪");
    }
  } catch (error) {
    console.error("启动游戏失败:", error);
  }
}

function stopEmulator() {
  try {
    console.log("🛑 开始停止模拟器...");

    // 首先暂停游戏
    if ((window as any).EJS_emulator) {
      try {
        // 尝试暂停游戏
        if ((window as any).EJS_emulator.pause) {
          (window as any).EJS_emulator.pause();
        }

        // 停止主循环
        if (
          (window as any).EJS_emulator.gameManager &&
          (window as any).EJS_emulator.gameManager.toggleMainLoop
        ) {
          (window as any).EJS_emulator.gameManager.toggleMainLoop(0);
        }

        // 停止音频上下文
        if (
          (window as any).EJS_emulator.Module &&
          (window as any).EJS_emulator.Module.AL
        ) {
          const alContext = (window as any).EJS_emulator.Module.AL.currentCtx;
          if (alContext && alContext.audioCtx) {
            console.log("🔇 停止音频上下文...");

            // 停止所有音频源
            if (alContext.sources) {
              Object.values(alContext.sources).forEach((source: any) => {
                try {
                  if (source.gain) {
                    source.gain.disconnect();
                  }
                  if (source.panner) {
                    source.panner.disconnect();
                  }
                } catch (e) {
                  console.log("停止音频源时出错:", e);
                }
              });
            }

            // 关闭音频上下文
            try {
              if (alContext.audioCtx.state !== "closed") {
                alContext.audioCtx.close();
              }
            } catch (e) {
              console.log("关闭音频上下文时出错:", e);
            }
          }
        }

        // 销毁模拟器实例
        (window as any).EJS_emulator.destroy();
      } catch (e) {
        console.log("销毁模拟器实例时出错:", e);
      }
      delete (window as any).EJS_emulator;
    }

    // 清理画布
    const canvas = document.querySelector("#canvas");
    if (canvas) {
      canvas.innerHTML = "";
    }

    // 清理所有音频元素
    const audioElements = document.querySelectorAll("audio");
    audioElements.forEach((audio) => {
      try {
        audio.pause();
        audio.currentTime = 0;
        audio.src = "";
        audio.load();
      } catch (e) {
        console.log("清理音频元素时出错:", e);
      }
    });

    console.log("✅ 模拟器已完全停止");
  } catch (error) {
    console.error("停止模拟器时出错:", error);
  }
  emit("stop");
}

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener("resize", handleOrientationChange);
  window.removeEventListener("orientationchange", handleOrientationChange);

  stopEmulator();
});
</script>
